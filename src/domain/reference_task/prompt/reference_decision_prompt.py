#!/usr/bin/env python3
"""
参考任务决策Prompt

基于参考成功案例的UI自动化测试决策prompt
参考decision_definition_prompt.py的编写风格和描述方式

Prompt块引用说明：
- get_reference_role_definition(): 角色定位 - 定义Agent的核心职责
- get_reference_test_case_description(): 测试用例信息 - 提供当前测试用例的详细信息
- _build_reference_actions_text(): 成功案例 - 提供参考的成功执行历史
- _build_current_execution_history(): 执行记忆 - 当前任务的执行记录
- get_image_element_extractor_prompt(): 图片元素提取器 - 指导如何从截图中提取相关元素
- get_interface_analysis_prompt(): 界面分析 - 指导如何分析当前界面
- get_reference_action_list(): 动作列表 - 可执行的动作类型和格式
- get_reference_learning_strategy(): 执行策略 - 如何学习和复用成功案例
- get_reference_exception_handling(): 异常场景 - 处理各种异常情况的策略
- get_reference_action_decision_prompt(): 动作决策 - 决策流程和步骤
- get_reference_output_example(): 输出格式 - JSON输出格式要求
- get_reference_output_requirement(): 输出要求 - 输出的具体要求
- get_reference_execution_invoke_prompt(): 执行流程 - 整体执行流程指导
- get_reference_user_invoke_prompt(): 用户调用提示 - 最终的执行和输出要求
"""
import json
import re

from src.domain.ui_task.mobile.repo.do.State import DeploymentState


def build_three_step_reference_decision_prompt(state: DeploymentState, current_step_description: str) -> str:
    """
    构建三步骤参考任务决策prompt，包含上一个、当前、下一个步骤的信息

    Args:
        state: 参考任务状态
        current_step_description: 当前步骤描述

    Returns:
        完整的系统prompt
    """
    # 获取步骤信息
    current_step_index = state.get("current_step_index", 0)
    task_steps = state.get("task_steps", [])

    # 构建三步骤测试用例信息
    three_step_test_case_info, current_step_name = get_three_step_test_case_description(state, current_step_index,
                                                                                        task_steps)
    print(three_step_test_case_info)

    # 构建前一个步骤和当前步骤的执行记忆
    combined_execution_history = _build_combined_execution_history(
        state.get("history", []), current_step_index
    )

    # 构建成功案例：按执行轮次顺序显示，包含当前步骤和下一步骤的成功案例
    combined_reference_text = _build_combined_reference_actions_text(
        state.get("reference_actions", []),
        current_step_description,
        task_steps[current_step_index + 1] if current_step_index + 1 < len(task_steps) else None
    )

    prompt = f"""
########## 角色定位 ##########
{get_three_step_reference_role_definition()}

########## 测试用例信息 ##########
{three_step_test_case_info}

########## 成功案例 ##########
{combined_reference_text}

########## 执行记忆 ##########
{combined_execution_history}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 动作列表 ##########
{get_reference_action_list()}

########## 异常场景 ##########
{get_reference_exception_handling()}

########## 动作决策 ##########
{get_three_step_reference_action_decision_prompt(current_step_name)}

########## 输出格式 ##########
{get_three_step_output_example()}

########## 输出要求 ##########
{get_reference_output_requirement()}
"""

    return prompt


def get_three_step_reference_role_definition() -> str:
    """获取三步骤参考任务的角色定义"""
    return """你是一个资深安卓软件测试专家，正在执行回归测试
**核心职责**
认真负责，参照<成功案例>完成回归测试"""


def get_three_step_description(previous_step_info: str, current_step_description: str, next_step_info: str) -> str:
    """获取三步骤描述"""
    step_info = f"**当前步骤**: {current_step_description}\n"
    if previous_step_info:
        step_info = f"**{previous_step_info}**\n" + step_info
    if next_step_info:
        step_info += f"**{next_step_info}**"
    return step_info


def get_three_step_reference_action_decision_prompt(current_step_name: str) -> str:
    """获取三步骤参考动作决策提示"""
    return f"""**严格按照顺序执行动作决策**
1. **分析当前界面**
   - 调用<界面分析>对<当前轮截图>进行详细分析
   - 如果当前界面无法找到目标元素，可参考对应的<成功案例截图>确定元素位置
   - 如果界面包含<异常场景>：参照<异常场景>进行处理

2. **测试路径偏离检测与纠正**
   - 如果<当前轮界面>与<成功案例截图>均不相符，且与<执行记忆>没有关联，说明脱离了测试路径
   - 将<当前轮界面>、<执行记忆>、<成功案例>结合分析，制定返回正确测试路径的方案
   
3. **记录执行进度**
   - 读取<执行记忆>时刻记录着回归测试执行进度，继续推进回归测试
   - 确保按照测试用例顺序执行，不允许重复执行已经完成的步骤；不允许跳过未执行过的步骤

4. **切换执行步骤**
   - 当前正在执行步骤: {current_step_name}
   - <成功案例><成功案例截图>包含了当前步骤所有轮次的执行记录和界面截图，同时包含了下一步骤的第一轮执行记录和界面截图
   - 将<当前轮截图>与下一步骤<成功案例><成功案例截图>进行对比，如果满足，则说明当前步骤已经完成，切换到下一步骤
   - 如果不满足,则继续参考当前步骤<成功案例>，继续执行当前步骤
   
5. **制定执行动作**
     - 参考对应<成功案例>的决策内容和动作，制定本轮执行动作
     - 动作坐标获取方式：必须参考<成功案例截图>中被红圈标记的位置(中心点为上次动作坐标)，在<当前轮截图>中找到对应位置元素，并以元素中心点作为坐标
     - 注意：<当前轮截图>与<成功案例截图>可能是不同手机截图，分辨率有所不同，只能参考元素特征和相对位置，不能参考具体坐标，计算坐标必须以<当前轮截图>为准

6. **执行结束判断**
   - **完成任务**：读取<执行记忆>和<测试用例信息>内容，如果所有步骤全部执行，且满足期望结果，则调用finished
   - **终止条件**：分析<执行记忆>最近5轮执行记录,出现以下任一情况立即调用failed
     * 路径偏离：连续5轮无法返回正确测试路径
     * 进度停滞：连续5轮界面分析结果完全相同
     * 重复操作：连续5次执行相同动作且界面无变化
     * 异常循环：连续5次遇到相同异常且无法解决"""


def get_step_reference_role_definition() -> str:
    """获取分步参考任务的角色定义"""
    return """你是一个资深安卓软件测试专家，正在执行分步回归测试
**核心职责**
专注执行当前步骤，参照<成功案例>中该步骤的所有轮内容进行精确复现"""


def get_three_step_test_case_description(state: DeploymentState, current_step_index: int, task_steps: list):
    """获取三步骤测试用例描述 - 显示全部步骤，标记上一轮执行的步骤"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")
    expected_result = state.get("expected_result", "")

    # 构建步骤信息，显示全部步骤
    step_info = ""
    current_step_name = ""

    for i, step in enumerate(task_steps):
        if i == current_step_index:
            current_step_name = step
            step_info += f"**{step}(当前执行步骤)**\n"
        else:
            # 其他步骤正常显示
            step_info += f"{step}\n"

    # 始终显示期望结果（如果存在）
    if expected_result:
        content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{step_info}- **期望结果**: {expected_result}"""
    else:
        content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{step_info}"""

    return content, current_step_name


def get_reference_test_case_description(state: DeploymentState) -> str:
    """获取参考任务的测试用例描述"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")
    content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{test_case_description}
- **期望结果**: {expected_result}"""
    return content


def get_current_step_description(step_description: str) -> str:
    """获取当前步骤描述"""
    return f"""{step_description}"""


def get_step_reference_action_decision_prompt() -> str:
    """获取分步参考动作决策提示"""
    return """**严格按照顺序执行动作决策**
1. **分析当前界面**
   - 调用<界面分析>对<当前轮截图>进行详细分析
   - 特别关注是否出现<异常场景>,优先处理<异常场景>

2. **判断步骤完成状态**
   - 首先将<当前轮截图>与成功案例下一步骤(决策内容与图片)对比
   - 如果两者界面UI布局或结构匹配，或决策内容描述的元素特征与位置信息在<当前轮截图>中存在，则认为步骤完成，调用finished
   - 如果不匹配，继续执行当前步骤

3. **匹配成功案例**
   - 将<当前轮截图>与<成功案例截图>对比，<当前轮截图>的<界面分析>结果与<成功案例>中找到界面分析对比，找到要参考的成功案例决策与动作

4. **执行决策**
   - 参照从<成功案例>找到的决策内容和动作，决策出本轮执行动作"""


def get_reference_role_definition() -> str:
    """获取参考任务的角色定义"""
    return """你是一个资深安卓软件测试专家，请你参照测试用例<成功案例>进行回归测试
**核心职责**
认真负责，参照<成功案例>完成回归测试"""


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
    return """结合<当前轮截图>和<测试用例信息> 的'用例步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
- 在界面中定位与<测试用例信息>的'用例步骤'的相关元素，必须严格按照'用例步骤'描述的**元素特征**和**位置信息**
- 优先根据 <测试用例信息>的'用例步骤'中描写的方位去聚焦并定位元素，要确定元素特征和位置的唯一性
- 禁止伪造、猜测不存在的元素和内容。"""


def get_reference_action_list() -> str:
    """获取参考任务的动作列表"""
    return """**必须补充动作参数，坐标必须使用标签<point>x1 y1</point>进行包裹**
click(point=<point>x1 y1</point>) # 必须补充点击位置坐标
long_press(point=<point>x1 y1</point>) # 必须补充长按位置坐标
type(content='text_to_input') # 输入文字内容，必须补充输入内容
scroll(point='<point>x1 y1</point>', direction='down or up or right or left') # 滑动屏幕，必须补充滑动起点坐标，和direction方向参数
drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') # 拖动元素，必须补充拖动起始坐标和结束坐标
wait(seconds=wait_seconds) # 等待，必须补充等待秒数
delete(content=delete_count) # 删除内容，必须补充删除文字数量
back() # 返回，用于返回上一级页面，或关闭弹窗
failed(content='reason') # 执行失败，必须补充失败原因
finished(content='success_message') # 执行成功，必须补充成功信息"""


def get_three_step_output_example() -> str:
    """获取三步骤模式的输出格式要求"""
    return """**严格按照以下JSON格式输出内容，保证字段内容纯净**
{{
"interface_analysis": "简要描述<当前轮截图>的<界面分析>结果",
"action_decision": "简要描述<动作决策>每一步决策过程",
"current_step_name": "当前执行步骤名称(不要步骤序号，仅步骤名称)，必须从<测试用例信息>的用例步骤列表中选择，不得自创步骤名称",
"current_step_index": "根据current_step_name值，从<测试用例信息>中获取步骤索引",
"action": "纯净的动作名称和参数,不包含任何多余内容"
}}

**重要约束**：
- current_step_name字段只能填写<测试用例信息>中明确列出的步骤名称
- 即使在处理弹窗、异常等情况时，也必须选择最相关的测试步骤名称
- 禁止创造新的步骤名称，如"关闭弹窗"、"处理异常"等
"""


def get_reference_output_requirement() -> str:
    """获取输出要求"""
    return """严格遵循<输出格式>输出规定字段和对应内容，并保证使用JSON格式输出内容
输出的内容必须是JSON格式，方便后续动作及参数的解析和执行
    """


def system_invoke_prompt() -> str:
    return """############ 执行流程 ##########
1.你需要先进行<界面分析>理解界面元素和内容;
2.结合<界面分析>结果，严格按照<动作决策>中步骤顺序执行，并得出"结果";
3.将结果严格按照<输出要求>进行输出最终结果;
"""


def _build_current_execution_history(history: list) -> str:
    """
    构建当前任务执行历史，兼容多种数据格式

    Args:
        history: 当前任务执行历史列表

    Returns:
        格式化的执行历史文本
    """
    if not history:
        return "当前任务刚开始，暂无执行历史"

    history_content = "**以下为你本次回归测试的执行记忆内容**\n"

    # 用于跟踪实际的显示轮次
    display_round = 1

    for i, record in enumerate(history):
        # 获取状态和失败信息
        record_status = record.get("status", "")

        # 跳过初始化记录
        if record.get("action") == "reference_task_initialization":
            continue

        # 显示执行记录
        if record_status != "blocked":
            formatted_record = _format_current_execution_record(record, display_round)
            if formatted_record:
                history_content += formatted_record
                display_round += 1  # 只有成功格式化的记录才增加轮次

    # 如果最终没有任何历史内容，返回默认信息
    if history_content == "**以下为你本次回归测试的执行记忆内容**\n":
        return "当前任务刚开始，暂无执行历史"

    return history_content


def _format_execution_record(record: dict, execution_count: int, is_next_step: bool = False,
                             step_description: str = "") -> str:
    """
    格式化单个执行记录为指定的中文格式

    Args:
        record: 执行记录字典
        execution_count: 执行轮次
        is_next_step: 是否为下一步骤的成功案例
        step_description: 步骤描述（用于下一步骤）

    Returns:
        格式化的执行记录文本
    """
    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    # 如果有parsed_fields，优先使用
    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        current_step_name = parsed_fields.get("current_step_name", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从decision_content或thought字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        # 优先使用thought字段（API返回格式）
        if thought_content:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_decision_content_new(
                decision_content)
    # 如果解析失败，尝试从record的其他字段获取信息
    if not current_step_name:
        current_step_name = record.get("step_name", "")

    if not action:
        action = record.get("action", "")

    if not action_decision:
        action_decision = "执行决策信息待补充"

    if not operation_instruction:
        operation_instruction = "操作指令信息待补充"

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not current_step_name and not action:
        return ""

    # 构建格式化文本
    if is_next_step:
        formatted_text = f"""**下一步骤成功案例(第{execution_count}轮执行)**
- 步骤描述: {step_description}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 操作执行: {operation_instruction}
- 执行动作: {action}
"""
    else:
        formatted_text = f"""**第{execution_count}轮执行**
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 操作执行: {operation_instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _format_current_execution_record(record: dict, execution_count: int) -> str:
    """
    格式化本次执行记忆记录，包含界面分析字段

    Args:
        record: 执行记录字典
        execution_count: 执行轮次

    Returns:
        格式化的执行记录文本
    """
    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    # 如果有parsed_fields，优先使用
    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        current_step_name = parsed_fields.get("current_step_name", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从decision_content或thought字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        # 优先使用thought字段（API返回格式）
        if thought_content:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, current_step_name, action_decision, operation_instruction, action = _parse_decision_content_with_interface(
                decision_content)

    # 如果解析失败，尝试从record的其他字段获取信息
    if not current_step_name:
        current_step_name = record.get("step_name", "")

    if not action:
        action = record.get("action", "")

    # 如果仍然没有关键信息，提供默认值
    if not interface_analysis:
        interface_analysis = ""

    if not action_decision:
        action_decision = ""

    if not operation_instruction:
        operation_instruction = ""

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not current_step_name and not action:
        return ""

    instruction = {"\n- 操作指令: " + operation_instruction if operation_instruction else ""}

    # 构建格式化文本（包含界面分析）
    formatted_text = f"""**第{execution_count}轮执行**
- 界面分析: {interface_analysis}
- 步骤名称: {current_step_name}
- 执行决策: {action_decision}{instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _parse_decision_content(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, action


def _parse_thought_content(thought_content: str) -> tuple:
    """
    从thought字段解析出五个字段

    Args:
        thought_content: thought字段内容，格式如：
        "界面分析: 界面顶部显示首页横幅...\n执行决策: 根据用例第一步...\n操作指令: 点击页面底部导航栏...\n执行动作: click"

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not thought_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 按行分割内容
    lines = thought_content.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith("界面分析:") or line.startswith("界面分析："):
            interface_analysis = re.sub(r'^界面分析[:：]\s*', '', line)
        elif line.startswith("执行决策:") or line.startswith("执行决策："):
            action_decision = re.sub(r'^执行决策[:：]\s*', '', line)
        elif line.startswith("操作指令:") or line.startswith("操作指令："):
            operation_instruction = re.sub(r'^操作指令[:：]\s*', '', line)
        elif line.startswith("执行动作:") or line.startswith("执行动作："):
            action = re.sub(r'^执行动作[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_new(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段（新格式）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_with_interface(decision_content: str) -> tuple:
    """
    从decision_content解析出五个字段（包含界面分析）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def _build_reference_actions_text(reference_actions: list) -> str:
    """
    构建参考任务动作列表的文本描述

    Args:
        reference_actions: 参考动作列表

    Returns:
        格式化的动作描述文本
    """
    if not reference_actions:
        return ""

    actions_text = ""

    for i, action in enumerate(reference_actions, 1):
        formatted_action = _format_execution_record(action, i)
        if formatted_action:
            actions_text += formatted_action

    return actions_text


def _build_combined_reference_actions_text(reference_actions: list, current_step_description: str,
                                           next_step_description: str = None) -> str:
    """
    构建组合的成功案例文本，按执行轮次顺序显示

    Args:
        reference_actions: 参考动作列表
        current_step_description: 当前步骤描述
        next_step_description: 下一步骤描述（可选）

    Returns:
        格式化的组合成功案例文本
    """
    if not reference_actions:
        return ""

    # 收集当前步骤和下一步骤的所有成功案例
    all_relevant_actions = []

    # 添加当前步骤的所有成功案例
    for action in reference_actions:
        action_step_name = action.get("step_name", "")
        if action_step_name and current_step_description:
            if _is_step_match(action_step_name, current_step_description):
                all_relevant_actions.append(action)

    # 添加下一步骤的第一轮成功案例
    if next_step_description:
        next_step_actions = []
        for action in reference_actions:
            action_step_name = action.get("step_name", "")
            if action_step_name and next_step_description:
                if _is_step_match(action_step_name, next_step_description):
                    next_step_actions.append(action)

        # 只添加下一步骤的第一轮
        if next_step_actions:
            all_relevant_actions.append(next_step_actions[0])

    if not all_relevant_actions:
        return "暂无相关成功案例"

    # 按轮次格式化所有成功案例
    actions_text = ""
    for i, action in enumerate(all_relevant_actions, 1):
        formatted_action = _format_execution_record_with_step_name(action, i)
        if formatted_action:
            actions_text += formatted_action

    return actions_text


def _format_execution_record_with_step_name(record: dict, execution_count: int) -> str:
    """
    格式化执行记录，包含步骤名称

    Args:
        record: 执行记录字典
        execution_count: 执行轮次

    Returns:
        格式化的执行记录文本
    """
    # 获取步骤名称
    step_name = record.get("step_name", "")

    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从其他字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        if thought_content:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_decision_content_new(
                decision_content)

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not step_name and not action:
        return ""

    # 构建格式化文本
    formatted_text = f"""**第{execution_count}轮执行**
- 步骤名称: {step_name}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 操作指令: {operation_instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _build_next_step_first_reference_text(reference_actions: list, next_step_description: str) -> str:
    """
    构建下一步骤的第一轮成功案例文本

    Args:
        reference_actions: 参考动作列表
        next_step_description: 下一步骤描述

    Returns:
        格式化的下一步骤第一轮成功案例文本
    """
    if not reference_actions:
        return ""

    # 过滤出下一步骤的参考动作，只取第一轮
    next_step_actions = []
    for action in reference_actions:
        action_step_name = action.get("step_name", "")
        if action_step_name and next_step_description:
            # 尝试多种匹配方式
            if _is_step_match(action_step_name, next_step_description):
                next_step_actions.append(action)

    if not next_step_actions:
        return ""

    # 只取第一轮执行
    first_action = next_step_actions[0]
    formatted_action = _format_execution_record(first_action, 1, is_next_step=True,
                                                step_description=next_step_description)

    return formatted_action if formatted_action else ""


def _is_step_match(action_step_name: str, current_step_description: str) -> bool:
    """
    判断动作步骤名称是否与当前步骤描述匹配
    使用简单的字符串匹配逻辑

    Args:
        action_step_name: 动作中的步骤名称
        current_step_description: 当前步骤描述

    Returns:
        是否匹配
    """
    if not action_step_name or not current_step_description:
        return False

    # 清理字符串
    action_name = action_step_name.strip()
    current_desc = current_step_description.strip()

    # 1. 完全匹配
    if action_name == current_desc:
        return True

    # 2. 去除序号后完全匹配
    action_name_no_num = re.sub(r'^\d+[.)、\-\s]*', '', action_name)
    current_desc_no_num = re.sub(r'^\d+[.)、\-\s]*', '', current_desc)

    if action_name_no_num == current_desc_no_num:
        return True

    # 3. 标准化后匹配（去除引号、括号等格式差异）
    action_normalized = _normalize_step_text(action_name_no_num)
    current_normalized = _normalize_step_text(current_desc_no_num)

    if action_normalized == current_normalized:
        return True

    return False


def _normalize_step_text(text: str) -> str:
    """
    标准化步骤文本，去除格式差异

    Args:
        text: 原始文本

    Returns:
        标准化后的文本
    """
    if not text:
        return ""

    # 去除各种引号和括号
    normalized = re.sub(r"['\"""（）()]", "", text)
    # 去除斜杠和特殊符号
    normalized = re.sub(r"[/\\]", "", normalized)
    # 去除多余空格
    normalized = re.sub(r'\s+', '', normalized)
    # 转换为小写
    normalized = normalized.lower()

    return normalized


def _build_combined_execution_history(history: list, current_step_index: int) -> str:
    """
    构建组合执行历史，包含所有执行记录

    Args:
        history: 执行历史列表
        current_step_index: 当前步骤索引（未使用，保持接口兼容）

    Returns:
        格式化的组合执行历史文本
    """
    if not history:
        return "暂无执行历史"

    combined_content = "**以下为你本次回归测试的执行记忆内容**\n"

    # 获取所有step_execution_with_reference的执行记录
    relevant_history = [r for r in history if r.get("action") == "step_execution_with_reference"]

    # 按时间戳排序
    relevant_history.sort(key=lambda x: x.get("timestamp", ""))

    execution_round = 0

    for record in relevant_history:
        ai_response = record.get("ai_response", "")
        action_command = record.get("action_command", "")
        step_description = record.get("step_description", "")

        try:
            if ai_response.strip().startswith('{') and ai_response.strip().endswith('}'):
                data = json.loads(ai_response)
                interface_analysis = data.get("interface_analysis", "")
                action_decision = data.get("action_decision", "")

                if interface_analysis and action_decision:
                    execution_round += 1

                    formatted_text = f"""**第{execution_round}轮执行**
- 步骤名称: {step_description}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 执行动作: {action_command}
"""
                    combined_content += _escape_template_variables(formatted_text)
        except (json.JSONDecodeError, Exception):
            continue

    if execution_round == 0:
        return "暂无执行历史"

    return combined_content


def get_reference_exception_handling() -> str:
    """获取参考任务异常场景处理策略"""
    return """* 如界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失；
* 如界面出现页面加载、动画、广告遮挡目标元素，则调用wait(seconds=3)等待加载完成或广告消失；
* 如界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗；
* 如界面出现弹窗，且弹窗不带倒计时，也不带任何可点击关闭的按钮，那么使用back尝试关闭弹窗；
* 如界面边缘存在悬浮气泡遮挡了目标元素位置，可以先通过拖动(drag)上/下移动悬浮气泡，露出目标元素；
* 如界面出现未加载完成、空白页面、页面切换中、异常页面，则调用wait(seconds=3)等待加载完成，其中若'空白页'和'异常页'出现连续多次(>2次)等待则考虑系统问题；
**特别注意**：弹窗处理优先级最高，必须确保弹窗处理完毕后，再进行其他操作"""
